import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Repository } from 'typeorm';
import { Invoice } from 'src/admin/invoice/invoice.entity';
import * as moment from 'moment-timezone';
import { ReportDailySaleSummaryQueryDTO } from './daily-received.dto';
import { InvoiceStatus } from 'src/core/enums/entity';
import Decimal from 'decimal.js';
import { InvoicePaymentMethod } from 'src/admin/invoice/invoice-payment-method.entity';
import { PaymentMethod } from 'src/admin/payment-method/payment-method.entity';

@Injectable()
export class ReportDailyReceivedService {
  constructor(
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(InvoicePaymentMethod)
    private invoicePaymentMethodRepo: Repository<InvoicePaymentMethod>,
    @InjectRepository(PaymentMethod)
    private paymentMethodRepo: Repository<PaymentMethod>,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      startDate,
      endDate,
      branchIds,
      clientZoneName,
    }: ReportDailySaleSummaryQueryDTO,
    isExport = false,
  ) {
    const start = moment.tz(startDate, clientZoneName).startOf('day');
    let end = moment.tz(endDate, clientZoneName);
    if (!start.isSame(end, 'day')) {
      end = end.endOf('day');
    }

    // Get all payment methods dynamically
    const paymentMethodList = await this.getPaymentMethodList();

    // Initialize payment data dynamically based on available payment methods
    const paymentData = {};
    paymentMethodList.forEach((method) => {
      paymentData[method.code] = 0;
    });

    const builderQueryPayment = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate: start.toDate(),
        endDate: end.toDate(),
      })
      .andWhere('invoice.id IS NOT NULL')
      .andWhere('invoice.status IN  (:...statusArr)', {
        statusArr: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      });

    if (branchIds && branchIds.length > 0) {
      builderQueryPayment.andWhere('invoice."branchId" IN (:...branchIds)', {
        branchIds,
      });
    }
    builderQueryPayment.orderBy('invoice.date', 'ASC');
    const invoices = await builderQueryPayment.getMany();

    let filteredInvoices = invoices;
    if (start.isSame(end, 'day')) {
      filteredInvoices = invoices.filter((invoice) => {
        if (!invoice.date) return false;
        const invoiceDate = moment.tz(invoice.date, clientZoneName);
        return invoiceDate.isSame(start, 'day');
      });
    }
    // map data with properties: date, customer, saleEmployee, saleTotal, received, tax, taxReceived, charge, netReceived, cash, card, others, credit, remark
    const filterInvoices = filteredInvoices.map((invoice) => {
      const date = invoice.date
        ? moment.tz(invoice.date, clientZoneName).format('DD/MM/YYYY')
        : '';
      let saleTotal = new Decimal(0);
      let received = new Decimal(0);
      let tax = new Decimal(0);
      let taxReceived = new Decimal(0);
      const charge = new Decimal(0);
      let netReceived = new Decimal(0);
      let cash = new Decimal(0);
      let card = new Decimal(0);
      let others = new Decimal(0);
      let credit = new Decimal(0);
      const due = new Decimal(0);
      // Initialize dynamic payment method variables
      const paymentMethodAmounts = {};
      paymentMethodList.forEach((method) => {
        paymentMethodAmounts[method.code] = new Decimal(0);
      });

      if (invoice.invoicePayments && invoice.invoicePayments.length > 0) {
        invoice.invoicePayments.forEach((payment) => {
          const paymentCode = payment.paymentMethod.code;
          const paymentAmount = payment.paid || 0;

          // Add to payment data tracking
          if (paymentData[paymentCode] !== undefined) {
            paymentData[paymentCode] += paymentAmount;
          }

          // Add to individual payment method amounts
          if (paymentMethodAmounts[paymentCode] !== undefined) {
            paymentMethodAmounts[paymentCode] =
              paymentMethodAmounts[paymentCode].plus(paymentAmount);
          }

          // Group by payment method type for summary columns
          if (payment.paymentMethod.type === 'cash') {
            cash = cash.plus(paymentAmount);
          } else if (payment.paymentMethod.type === 'card') {
            card = card.plus(paymentAmount);
          } else if (payment.paymentMethod.type === 'others') {
            if (payment.paymentMethod.code === 'credits') {
              credit = credit.plus(paymentAmount);
            } else {
              others = others.plus(paymentAmount);
            }
          }
          if (payment.paymentMethod.code !== 'credits') {
            received = received.plus(payment.paid);
          }
        });
      }
      netReceived = received.div(1.09);
      tax = received.minus(netReceived);
      taxReceived = tax;
      saleTotal = received;
      return {
        date: date,
        referenceNo: invoice.code ? `IN${invoice.code}` : '',
        customer: invoice.customer
          ? invoice.customer.firstName + ' ' + invoice.customer.lastName
          : '',
        saleEmployee:
          invoice?.referral?.username ||
          invoice?.referral?.displayName ||
          invoice?.referral?.fullname ||
          '',
        saleTotal: saleTotal.toFixed(2),
        received: received.toFixed(2),
        tax: tax.toFixed(2),
        taxReceived: taxReceived.toFixed(2),
        charge: charge.toFixed(2),
        netReceived: netReceived.toFixed(2),
        cash: cash.toFixed(2),
        card: card.toFixed(2),
        others: others.toFixed(2),
        credit: credit.toFixed(2),
        due: due.toFixed(2),
        remark: invoice.note,
        customerCode: invoice.customer ? invoice.customer.code : '',
        formatCustomer: `${invoice.customer?.code} ${invoice.customer?.firstName} ${invoice.customer?.lastName}`,
        // Add dynamic payment method amounts
        ...Object.fromEntries(
          paymentMethodList.map((method) => [
            method.code,
            paymentMethodAmounts[method.code]?.toFixed(2) || '0.00',
          ]),
        ),
      };
    });

    //group by date
    const groupByDate = filterInvoices.reduce((acc, item) => {
      if (!acc[item.date]) {
        acc[item.date] = [];
      }
      acc[item.date].push(item);
      return acc;
    }, {});

    // push 1 object daily total
    const granTotal: any = {
      saleTotal: 0,
      received: 0,
      tax: 0,
      taxReceived: 0,
      charge: 0,
      netReceived: 0,
      cash: 0,
      card: 0,
      others: 0,
      credit: 0,
      due: 0,
      saleEmployee: 'daily_total',
      // Initialize dynamic payment method totals
      ...Object.fromEntries(
        paymentMethodList.map((method) => [method.code, 0]),
      ),
    };

    // return groupByDate;
    Object.keys(groupByDate).forEach((key) => {
      const sumTotal = groupByDate[key].reduce(
        (acc, item) => {
          acc.saleTotal = new Decimal(acc.saleTotal)
            .plus(item.saleTotal)
            .toFixed(2);
          acc.received = new Decimal(acc.received)
            .plus(item.received)
            .toFixed(2);
          acc.tax = new Decimal(acc.tax).plus(item.tax).toFixed(2);
          acc.taxReceived = new Decimal(acc.taxReceived)
            .plus(item.taxReceived)
            .toFixed(2);
          acc.charge = new Decimal(acc.charge).plus(item.charge).toFixed(2);
          acc.netReceived = new Decimal(acc.netReceived)
            .plus(item.netReceived)
            .toFixed(2);
          acc.cash = new Decimal(acc.cash).plus(item.cash).toFixed(2);
          acc.card = new Decimal(acc.card).plus(item.card).toFixed(2);
          acc.others = new Decimal(acc.others).plus(item.others).toFixed(2);
          acc.credit = new Decimal(acc.credit).plus(item.credit).toFixed(2);
          acc.due = new Decimal(acc.due).plus(item.due).toFixed(2);
          acc.saleEmployee = 'daily_total';

          // Add dynamic payment method totals
          paymentMethodList.forEach((method) => {
            const methodCode = method.code;
            if (
              item[methodCode] !== undefined &&
              acc[methodCode] !== undefined
            ) {
              acc[methodCode] = new Decimal(acc[methodCode])
                .plus(item[methodCode])
                .toFixed(2);
            }
          });

          return acc;
        },
        {
          date: key,
          saleTotal: 0,
          received: 0,
          tax: 0,
          taxReceived: 0,
          charge: 0,
          netReceived: 0,
          cash: 0,
          card: 0,
          others: 0,
          credit: 0,
          due: 0,
          saleEmployee: 'daily_total',
          // Initialize dynamic payment method totals
          ...Object.fromEntries(
            paymentMethodList.map((method) => [method.code, 0]),
          ),
        },
      );
      granTotal.saleEmployee = 'grand_total';
      granTotal.saleTotal = new Decimal(granTotal.saleTotal)
        .plus(sumTotal.saleTotal)
        .toFixed(2);
      granTotal.received = new Decimal(granTotal.received)
        .plus(sumTotal.received)
        .toFixed(2);
      granTotal.tax = new Decimal(granTotal.tax).plus(sumTotal.tax).toFixed(2);
      granTotal.taxReceived = new Decimal(granTotal.taxReceived)
        .plus(sumTotal.taxReceived)
        .toFixed(2);
      granTotal.charge = new Decimal(granTotal.charge)
        .plus(sumTotal.charge)
        .toFixed(2);
      granTotal.netReceived = new Decimal(granTotal.netReceived)
        .plus(sumTotal.netReceived)
        .toFixed(2);
      granTotal.cash = new Decimal(granTotal.cash)
        .plus(sumTotal.cash)
        .toFixed(2);
      granTotal.card = new Decimal(granTotal.card)
        .plus(sumTotal.card)
        .toFixed(2);
      granTotal.others = new Decimal(granTotal.others)
        .plus(sumTotal.others)
        .toFixed(2);
      granTotal.credit = new Decimal(granTotal.credit)
        .plus(sumTotal.credit)
        .toFixed(2);
      granTotal.due = new Decimal(granTotal.due).plus(sumTotal.due).toFixed(2);

      // Add dynamic payment method grand totals
      paymentMethodList.forEach((method) => {
        const methodCode = method.code;
        if (
          sumTotal[methodCode] !== undefined &&
          granTotal[methodCode] !== undefined
        ) {
          granTotal[methodCode] = new Decimal(granTotal[methodCode])
            .plus(sumTotal[methodCode])
            .toFixed(2);
        }
      });

      groupByDate[key].push(sumTotal);
    });
    groupByDate['grand_total'] = [granTotal];

    // get summary for payment details
    const paymentDetail = paymentMethodList.map((paymentMethod) => {
      let totalPaid = new Decimal(0).toFixed(2);
      totalPaid = paymentData[paymentMethod.code]
        ? paymentData[paymentMethod.code].toFixed(2)
        : totalPaid;
      return {
        method_name: paymentMethod.name,
        total_paid: totalPaid,
      };
    });

    if (isExport) {
      const exportData = [];
      Object.keys(groupByDate).forEach((key) => {
        if (key !== 'grand_total') {
          exportData.push({
            charge: key,
          });
        }

        for (let i = 0; i < groupByDate[key].length; i++) {
          if (groupByDate[key][i].saleEmployee == 'daily_total') {
            groupByDate[key][i].saleEmployee = 'DAILY TOTAL:';
          }
          if (groupByDate[key][i].saleEmployee == 'grand_total') {
            groupByDate[key][i].saleEmployee = 'GRAND TOTAL:';
          }

          exportData.push(groupByDate[key][i]);
        }
      });

      // add payment detail
      if (paymentDetail.length > 0) {
        exportData.push({
          date: '',
          referenceNo: '',
          customer: '',
          saleEmployee: '',
          saleTotal: '',
          received: '',
          tax: 'PAYMENT DETAIL:',
          taxReceived: '',
          charge: '',
          netReceived: '',
          cash: '',
          card: '',
          others: '',
          credit: '',
          due: '',
          remark: '',
        });
        paymentDetail.forEach((payment) => {
          exportData.push({
            date: '',
            referenceNo: '',
            customer: '',
            saleEmployee: '',
            saleTotal: '',
            received: '',
            tax: payment.method_name,
            taxReceived: payment.total_paid,
            charge: '',
            netReceived: '',
            cash: '',
            card: '',
            others: '',
            credit: '',
            due: '',
            remark: '',
          });
        });
      }

      return exportData;
    }

    return {
      data: Object.keys(groupByDate).length > 1 ? [groupByDate] : [],
      payment_detail: paymentDetail,
    };
  }

  async getPaymentMethodList() {
    const paymentMethodList = await this.paymentMethodRepo
      .createQueryBuilder('payment_method')
      .select(['name', 'code'])
      .orderBy('created', 'DESC')
      .getRawMany();
    return paymentMethodList;
  }
}
